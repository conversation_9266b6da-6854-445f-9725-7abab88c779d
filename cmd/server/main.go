package main

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/99designs/gqlgen/graphql/playground"
	_ "github.com/lib/pq"

	"xbit-cdn-service/internal/config"
	"xbit-cdn-service/internal/handler"
	"xbit-cdn-service/internal/repository"
	"xbit-cdn-service/internal/service"
	"xbit-cdn-service/pkg/auth"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database connection
	db, err := initDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Initialize services
	r2Service, err := service.NewR2Service(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize R2 service: %v", err)
	}

	// Initialize CDN service (optional)
	cdnService, err := service.NewCDNService(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize CDN service: %v", err)
	}

	// Initialize repositories
	fileRepo := repository.NewFileRepository(db)

	// Initialize services
	fileService := service.NewFileService(fileRepo, r2Service, cdnService, cfg)

	// Initialize JWT service
	jwtService := auth.NewJWTService(cfg.JWT.Secret, cfg.JWT.Expiry)

	// Initialize GraphQL handler
	graphqlHandler := handler.NewGraphQLHandler(fileService)

	// Create HTTP server
	srv := createServer(graphqlHandler, jwtService, cfg)

	// Start server
	log.Printf("Server starting on port %s", cfg.Server.Port)
	log.Printf("GraphQL playground available at http://localhost:%s/playground", cfg.Server.Port)
	log.Printf("GraphQL endpoint available at http://localhost:%s/graphql", cfg.Server.Port)

	if err := srv.ListenAndServe(); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

func initDatabase(cfg *config.Config) (*sql.DB, error) {
	// For now, we'll use environment variables for database connection
	// In production, you might want to add these to your config
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")
	dbSSLMode := os.Getenv("DB_SSLMODE")

	// Set defaults
	if dbHost == "" {
		dbHost = "localhost"
	}
	if dbPort == "" {
		dbPort = "5432"
	}
	if dbUser == "" {
		dbUser = "postgres"
	}
	if dbName == "" {
		dbName = "xbit_cdn"
	}
	if dbSSLMode == "" {
		dbSSLMode = "disable"
	}

	// Build connection string
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	// Open database connection
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connection established")
	return db, nil
}

func createServer(graphqlHandler *handler.GraphQLHandler, jwtService *auth.JWTService, cfg *config.Config) *http.Server {
	mux := http.NewServeMux()

	// Initialize auth middleware and handlers
	authMiddleware := auth.NewAuthMiddleware(jwtService, false)
	authHandler := auth.NewAuthHandler(jwtService)
	graphqlAuthMiddleware := auth.NewGraphQLAuthMiddleware(jwtService)

	// Authentication endpoints
	mux.HandleFunc("/auth/login", authHandler.Login)
	mux.HandleFunc("/auth/refresh", authHandler.Refresh)
	mux.Handle("/auth/me", authMiddleware.RequireAuth(http.HandlerFunc(authHandler.Me)))

	// GraphQL endpoint with authentication context
	mux.HandleFunc("/graphql", func(w http.ResponseWriter, r *http.Request) {
		// Extract user context for GraphQL
		ctx := graphqlAuthMiddleware.ExtractUserFromRequest(r)
		r = r.WithContext(ctx)

		// For now, return a simple response
		// TODO: Implement proper GraphQL handling with resolvers
		w.Header().Set("Content-Type", "application/json")

		if r.Method == "GET" {
			// Handle introspection or simple queries
			fmt.Fprintf(w, `{"data": {"health": "OK"}}`)
		} else if r.Method == "POST" {
			// Handle mutations and complex queries
			fmt.Fprintf(w, `{"data": {"health": "OK"}}`)
		} else {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})

	// GraphQL playground
	mux.Handle("/playground", playground.Handler("GraphQL playground", "/graphql"))

	// Health check endpoint (no auth required)
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "service": "xbit-cdn-service"}`)
	})

	// File upload endpoint (requires authentication)
	mux.Handle("/upload", authMiddleware.RequirePermission(auth.PermissionWriteFiles)(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Method != "POST" {
				http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
				return
			}

			// TODO: Implement file upload handling
			w.Header().Set("Content-Type", "application/json")
			fmt.Fprintf(w, `{"message": "Upload endpoint - TODO: implement"}`)
		}),
	))

	// Apply CORS middleware to all routes
	handler := auth.CORSMiddleware(mux)

	return &http.Server{
		Addr:    ":" + cfg.Server.Port,
		Handler: handler,
	}
}

// CORS middleware
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// JWT middleware (placeholder)
func jwtMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// TODO: Implement JWT token validation
		// For now, just pass through
		next.ServeHTTP(w, r)
	})
}
