# XBIT CDN Service Makefile

# Variables
APP_NAME=xbit-cdn-service
DOCKER_IMAGE=$(APP_NAME)
DOCKER_TAG=latest
BINARY_NAME=bin/server

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build the application
.PHONY: build
build:
	$(GOBUILD) -o $(BINARY_NAME) cmd/server/main.go

# Run the application
.PHONY: run
run: build
	./$(BINARY_NAME)

# Run with hot reload (requires air: go install github.com/cosmtrek/air@latest)
.PHONY: dev
dev:
	air

# Clean build artifacts
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)

# Run tests
.PHONY: test
test:
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# Download dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Docker commands
.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 --env-file .env $(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-compose-up
docker-compose-up:
	docker-compose up -d

.PHONY: docker-compose-down
docker-compose-down:
	docker-compose down

.PHONY: docker-compose-logs
docker-compose-logs:
	docker-compose logs -f

# Database commands
.PHONY: db-migrate
db-migrate:
	@echo "Running database migrations..."
	@if [ -f .env ]; then \
		export $$(cat .env | xargs) && \
		psql "postgresql://$$DB_USER:$$DB_PASSWORD@$$DB_HOST:$$DB_PORT/$$DB_NAME?sslmode=$$DB_SSLMODE" -f migrations/001_create_files_table.sql; \
	else \
		echo "Please create .env file with database configuration"; \
	fi

.PHONY: db-reset
db-reset:
	@echo "Resetting database..."
	@if [ -f .env ]; then \
		export $$(cat .env | xargs) && \
		psql "postgresql://$$DB_USER:$$DB_PASSWORD@$$DB_HOST:$$DB_PORT/$$DB_NAME?sslmode=$$DB_SSLMODE" -c "DROP TABLE IF EXISTS files CASCADE;"; \
		make db-migrate; \
	else \
		echo "Please create .env file with database configuration"; \
	fi

# Linting and formatting
.PHONY: fmt
fmt:
	$(GOCMD) fmt ./...

.PHONY: lint
lint:
	golangci-lint run

# Generate GraphQL code (if using gqlgen)
.PHONY: generate
generate:
	$(GOCMD) run github.com/99designs/gqlgen generate

# Install development tools
.PHONY: install-tools
install-tools:
	$(GOGET) github.com/cosmtrek/air@latest
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/99designs/gqlgen@latest

# Setup development environment
.PHONY: setup
setup: install-tools deps
	@echo "Development environment setup complete!"
	@echo "1. Copy .env.example to .env and configure your settings"
	@echo "2. Run 'make docker-compose-up' to start dependencies"
	@echo "3. Run 'make db-migrate' to setup database"
	@echo "4. Run 'make dev' to start development server"

# Production deployment
.PHONY: deploy
deploy: docker-build
	@echo "Deploying to production..."
	@echo "This is a placeholder - implement your deployment strategy"

# Health check
.PHONY: health
health:
	@curl -f http://localhost:8080/health || echo "Service is not healthy"

# Show help
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build              Build the application"
	@echo "  run                Build and run the application"
	@echo "  dev                Run with hot reload (requires air)"
	@echo "  clean              Clean build artifacts"
	@echo "  test               Run tests"
	@echo "  test-coverage      Run tests with coverage report"
	@echo "  deps               Download and tidy dependencies"
	@echo "  docker-build       Build Docker image"
	@echo "  docker-run         Run Docker container"
	@echo "  docker-compose-up  Start all services with docker-compose"
	@echo "  docker-compose-down Stop all services"
	@echo "  docker-compose-logs Show logs from all services"
	@echo "  db-migrate         Run database migrations"
	@echo "  db-reset           Reset database and run migrations"
	@echo "  fmt                Format Go code"
	@echo "  lint               Run linter"
	@echo "  generate           Generate GraphQL code"
	@echo "  install-tools      Install development tools"
	@echo "  setup              Setup development environment"
	@echo "  deploy             Deploy to production"
	@echo "  health             Check service health"
	@echo "  help               Show this help message"
