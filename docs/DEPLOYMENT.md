# Deployment Guide

This guide covers different deployment strategies for the XBIT CDN Service.

## Prerequisites

- Docker and Docker Compose
- PostgreSQL database
- Cloudflare R2 bucket and credentials
- Domain name (for production)
- SSL certificates (for HTTPS)

## Environment Setup

### 1. Environment Variables

Create environment-specific `.env` files:

```bash
# .env.development
ENV=development
PORT=8080
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=dev_password
DB_NAME=xbit_cdn_dev
# ... other variables

# .env.staging
ENV=staging
PORT=8080
DB_HOST=staging-db.example.com
# ... other variables

# .env.production
ENV=production
PORT=8080
DB_HOST=prod-db.example.com
# ... other variables
```

### 2. Database Setup

#### Local Development

```bash
# Start PostgreSQL with Docker
docker run -d \
  --name xbit-cdn-postgres \
  -e POSTGRES_DB=xbit_cdn \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres123 \
  -p 5432:5432 \
  postgres:15-alpine

# Run migrations
make db-migrate
```

#### Production Database

For production, use a managed database service:

- **AWS RDS**
- **Google Cloud SQL**
- **Azure Database for PostgreSQL**
- **DigitalOcean Managed Databases**

## Deployment Options

### Option 1: Docker Compose (Recommended for small to medium deployments)

#### Development

```bash
# Start all services
make docker-compose-up

# View logs
make docker-compose-logs

# Stop services
make docker-compose-down
```

#### Production

1. **Create production docker-compose file**

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  xbit-cdn-service:
    image: your-registry/xbit-cdn-service:latest
    container_name: xbit-cdn-service-prod
    ports:
      - "8080:8080"
    environment:
      ENV: production
      # ... production environment variables
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: xbit-cdn-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - xbit-cdn-service
    restart: unless-stopped
```

2. **Deploy to production**

```bash
# Build and push image
docker build -t your-registry/xbit-cdn-service:latest .
docker push your-registry/xbit-cdn-service:latest

# Deploy on production server
docker-compose -f docker-compose.prod.yml up -d
```

### Option 2: Kubernetes

#### 1. Create Kubernetes manifests

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: xbit-cdn

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: xbit-cdn-config
  namespace: xbit-cdn
data:
  PORT: "8080"
  ENV: "production"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "xbit_cdn"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: xbit-cdn-secrets
  namespace: xbit-cdn
type: Opaque
stringData:
  DB_PASSWORD: "your-db-password"
  R2_ACCESS_KEY_ID: "your-r2-access-key"
  R2_SECRET_ACCESS_KEY: "your-r2-secret-key"
  JWT_SECRET: "your-jwt-secret"

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: xbit-cdn-service
  namespace: xbit-cdn
spec:
  replicas: 3
  selector:
    matchLabels:
      app: xbit-cdn-service
  template:
    metadata:
      labels:
        app: xbit-cdn-service
    spec:
      containers:
      - name: xbit-cdn-service
        image: your-registry/xbit-cdn-service:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: xbit-cdn-config
        - secretRef:
            name: xbit-cdn-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: xbit-cdn-service
  namespace: xbit-cdn
spec:
  selector:
    app: xbit-cdn-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: xbit-cdn-ingress
  namespace: xbit-cdn
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: xbit-cdn-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: xbit-cdn-service
            port:
              number: 80
```

#### 2. Deploy to Kubernetes

```bash
# Apply all manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n xbit-cdn
kubectl get services -n xbit-cdn
kubectl get ingress -n xbit-cdn

# View logs
kubectl logs -f deployment/xbit-cdn-service -n xbit-cdn
```

### Option 3: Cloud Platforms

#### AWS ECS

1. **Create task definition**
2. **Set up ECS service**
3. **Configure Application Load Balancer**
4. **Set up RDS for database**

#### Google Cloud Run

```bash
# Build and push to Google Container Registry
gcloud builds submit --tag gcr.io/PROJECT-ID/xbit-cdn-service

# Deploy to Cloud Run
gcloud run deploy xbit-cdn-service \
  --image gcr.io/PROJECT-ID/xbit-cdn-service \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars ENV=production,PORT=8080
```

#### Azure Container Instances

```bash
# Create resource group
az group create --name xbit-cdn-rg --location eastus

# Deploy container
az container create \
  --resource-group xbit-cdn-rg \
  --name xbit-cdn-service \
  --image your-registry/xbit-cdn-service:latest \
  --dns-name-label xbit-cdn \
  --ports 8080 \
  --environment-variables ENV=production PORT=8080
```

## SSL/TLS Configuration

### Let's Encrypt with Certbot

```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Manual SSL Configuration

```nginx
# nginx.prod.conf
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://xbit-cdn-service:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## Monitoring and Logging

### Health Checks

```bash
# Application health
curl -f http://localhost:8080/health

# Database health
docker exec xbit-cdn-postgres pg_isready -U postgres

# Container health
docker ps --filter "name=xbit-cdn"
```

### Logging

#### Centralized Logging with ELK Stack

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.14.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

#### Application Logging

```go
// Add to your Go application
import (
    "github.com/sirupsen/logrus"
)

func init() {
    logrus.SetFormatter(&logrus.JSONFormatter{})
    logrus.SetLevel(logrus.InfoLevel)
}
```

## Backup and Recovery

### Database Backup

```bash
# Create backup
docker exec xbit-cdn-postgres pg_dump -U postgres xbit_cdn > backup.sql

# Restore backup
docker exec -i xbit-cdn-postgres psql -U postgres xbit_cdn < backup.sql

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker exec xbit-cdn-postgres pg_dump -U postgres xbit_cdn > "backup_${DATE}.sql"
aws s3 cp "backup_${DATE}.sql" s3://your-backup-bucket/
```

### File Storage Backup

Since files are stored in Cloudflare R2, they are automatically replicated. However, you can create additional backups:

```bash
# Sync R2 bucket to another location
rclone sync r2:your-bucket s3:backup-bucket
```

## Performance Optimization

### Database Optimization

```sql
-- Add indexes for better query performance
CREATE INDEX CONCURRENTLY idx_files_user_id_status ON files(user_id, status);
CREATE INDEX CONCURRENTLY idx_files_created_at ON files(created_at DESC);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM files WHERE user_id = 'user-id' AND status = 'READY';
```

### Application Optimization

1. **Connection Pooling**
   ```go
   db.SetMaxOpenConns(25)
   db.SetMaxIdleConns(25)
   db.SetConnMaxLifetime(5 * time.Minute)
   ```

2. **Caching with Redis**
   ```go
   // Cache file metadata
   rdb.Set(ctx, "file:"+fileID, fileJSON, time.Hour)
   ```

3. **CDN Configuration**
   - Set appropriate cache headers
   - Configure edge caching rules
   - Use image optimization

## Security Considerations

### Network Security

1. **Firewall Rules**
   - Only allow necessary ports (80, 443, 22)
   - Restrict database access to application servers only

2. **VPC/Network Isolation**
   - Use private subnets for databases
   - Implement security groups/network policies

### Application Security

1. **Environment Variables**
   - Never commit secrets to version control
   - Use secret management services (AWS Secrets Manager, etc.)

2. **JWT Security**
   - Use strong secret keys
   - Implement token rotation
   - Set appropriate expiration times

3. **File Upload Security**
   - Validate file types and sizes
   - Scan for malware
   - Use signed URLs for sensitive content

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   docker exec xbit-cdn-service nc -zv postgres 5432
   
   # Check database logs
   docker logs xbit-cdn-postgres
   ```

2. **R2 Upload Failures**
   ```bash
   # Test R2 connectivity
   aws s3 ls --endpoint-url=https://your-account.r2.cloudflarestorage.com
   
   # Check credentials
   aws configure list
   ```

3. **High Memory Usage**
   ```bash
   # Monitor container resources
   docker stats xbit-cdn-service
   
   # Check for memory leaks
   go tool pprof http://localhost:8080/debug/pprof/heap
   ```

### Debugging

1. **Enable Debug Logging**
   ```bash
   export LOG_LEVEL=debug
   ```

2. **Health Check Endpoints**
   ```bash
   curl http://localhost:8080/health
   curl http://localhost:8080/debug/vars
   ```

3. **Database Queries**
   ```sql
   -- Check active connections
   SELECT * FROM pg_stat_activity WHERE datname = 'xbit_cdn';
   
   -- Check slow queries
   SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;
   ```

## Rollback Strategy

### Application Rollback

```bash
# Rollback to previous version
docker-compose down
docker pull your-registry/xbit-cdn-service:previous-tag
docker-compose up -d

# Kubernetes rollback
kubectl rollout undo deployment/xbit-cdn-service -n xbit-cdn
```

### Database Rollback

```bash
# Restore from backup
docker exec -i xbit-cdn-postgres psql -U postgres xbit_cdn < backup_previous.sql
```

## Maintenance

### Regular Maintenance Tasks

1. **Update Dependencies**
   ```bash
   go get -u ./...
   go mod tidy
   ```

2. **Database Maintenance**
   ```sql
   -- Vacuum and analyze
   VACUUM ANALYZE files;
   
   -- Reindex
   REINDEX TABLE files;
   ```

3. **Log Rotation**
   ```bash
   # Configure logrotate
   /var/log/xbit-cdn/*.log {
       daily
       rotate 30
       compress
       delaycompress
       missingok
       notifempty
   }
   ```

4. **Certificate Renewal**
   ```bash
   # Check certificate expiry
   openssl x509 -in /etc/nginx/ssl/cert.pem -text -noout | grep "Not After"
   
   # Renew Let's Encrypt certificates
   certbot renew
   ```
