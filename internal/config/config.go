package config

import (
	"os"
	"strconv"
	"time"
)

type Config struct {
	Server ServerConfig
	R2     R2Config
	CDN    CDNConfig
	JWT    JWTConfig
	Upload UploadConfig
}

type ServerConfig struct {
	Port string
	Env  string
}

type R2Config struct {
	AccountID       string
	AccessKeyID     string
	SecretAccessKey string
	BucketName      string
	Endpoint        string
}

type CDNConfig struct {
	BaseURL   string
	ZoneID    string
	APIToken  string
}

type JWTConfig struct {
	Secret string
	Expiry time.Duration
}

type UploadConfig struct {
	MaxFileSize        int64
	AllowedExtensions  []string
	SignedURLExpiry    time.Duration
}

func Load() *Config {
	return &Config{
		Server: ServerConfig{
			Port: getEnv("PORT", "8080"),
			Env:  getEnv("ENV", "development"),
		},
		R2: R2Config{
			AccountID:       getEnv("R2_ACCOUNT_ID", ""),
			AccessKeyID:     getEnv("R2_ACCESS_KEY_ID", ""),
			SecretAccessKey: getEnv("R2_SECRET_ACCESS_KEY", ""),
			BucketName:      getEnv("R2_BUCKET_NAME", ""),
			Endpoint:        getEnv("R2_ENDPOINT", ""),
		},
		CDN: CDNConfig{
			BaseURL:  getEnv("CDN_BASE_URL", ""),
			ZoneID:   getEnv("CDN_ZONE_ID", ""),
			APIToken: getEnv("CDN_API_TOKEN", ""),
		},
		JWT: JWTConfig{
			Secret: getEnv("JWT_SECRET", "default-secret"),
			Expiry: parseDuration(getEnv("JWT_EXPIRY", "24h")),
		},
		Upload: UploadConfig{
			MaxFileSize:       parseSize(getEnv("MAX_FILE_SIZE", "100MB")),
			AllowedExtensions: []string{"jpg", "jpeg", "png", "gif", "mp4", "mov", "avi", "webm"},
			SignedURLExpiry:   parseDuration(getEnv("SIGNED_URL_EXPIRY", "1h")),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func parseDuration(s string) time.Duration {
	d, err := time.ParseDuration(s)
	if err != nil {
		return time.Hour
	}
	return d
}

func parseSize(s string) int64 {
	// Simple parser for sizes like "100MB"
	if len(s) < 3 {
		return 100 * 1024 * 1024 // Default 100MB
	}
	
	unit := s[len(s)-2:]
	numStr := s[:len(s)-2]
	num, err := strconv.ParseInt(numStr, 10, 64)
	if err != nil {
		return 100 * 1024 * 1024
	}
	
	switch unit {
	case "KB":
		return num * 1024
	case "MB":
		return num * 1024 * 1024
	case "GB":
		return num * 1024 * 1024 * 1024
	default:
		return num
	}
}
