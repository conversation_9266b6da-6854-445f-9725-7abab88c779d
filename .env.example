# Server Configuration
PORT=8080
ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=xbit_cdn
DB_SSLMODE=disable

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com

# Cloudflare CDN Configuration
CDN_BASE_URL=https://your-cdn-domain.com
CDN_ZONE_ID=your_zone_id
CDN_API_TOKEN=your_api_token

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRY=24h

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp4,mov,avi,webm
SIGNED_URL_EXPIRY=1h
